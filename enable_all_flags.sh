#!/bin/bash

# Enable all feature flags for local development
echo "🚀 Enabling all feature flags for local development..."

cd backend

# List of all feature flags to enable
FLAGS=(
    "custom_agents"
    "agent_marketplace" 
    "knowledge_base"
    "workflows"
)

# Enable each flag
for flag in "${FLAGS[@]}"; do
    echo "Enabling $flag..."
    python flags/setup.py enable "$flag" "Enabled for local development"
done

echo ""
echo "✅ All feature flags enabled! Current status:"
echo ""
python flags/setup.py list
