#!/bin/bash

# Enable all feature flags for local Redis development
echo "🚀 Enabling all feature flags for local Redis..."

# List of all feature flags to enable
FLAGS=(
    "custom_agents"
    "agent_marketplace" 
    "knowledge_base"
    "workflows"
)

# Get current timestamp
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%S.%6NZ")

# Enable each flag using Redis CLI
for flag in "${FLAGS[@]}"; do
    echo "Enabling $flag..."
    
    # Set the feature flag hash with enabled=true, description, and timestamp
    redis-cli HSET "feature_flag:$flag" \
        "enabled" "true" \
        "description" "Enabled for local development" \
        "updated_at" "$TIMESTAMP"
    
    # Add the flag to the flags list set
    redis-cli SADD "feature_flags:list" "$flag"
done

echo ""
echo "✅ All feature flags enabled! Current status:"
echo ""

# List all flags and their status
for flag in "${FLAGS[@]}"; do
    enabled=$(redis-cli HGET "feature_flag:$flag" "enabled")
    description=$(redis-cli HGET "feature_flag:$flag" "description")
    updated_at=$(redis-cli HGET "feature_flag:$flag" "updated_at")
    
    if [ "$enabled" = "true" ]; then
        status_icon="✓"
        status_text="ENABLED"
    else
        status_icon="✗"
        status_text="DISABLED"
    fi
    
    echo "$status_icon $flag: $status_text"
    echo "  Description: $description"
    echo "  Updated: $updated_at"
    echo ""
done
